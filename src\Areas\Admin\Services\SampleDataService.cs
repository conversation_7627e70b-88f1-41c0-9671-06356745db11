using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using System.Diagnostics;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Service for generating sample data for testing purposes
/// Only used in development environment
/// </summary>
public class SampleDataService
{
    private readonly AppDbContext _context;
    private readonly IWebHostEnvironment _environment;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ReferralService _referralService;
    private readonly ILogger<SampleDataService> _logger;
    private readonly Random _random = new Random();

    public SampleDataService(
        AppDbContext context,
        IWebHostEnvironment environment,
        IHttpContextAccessor httpContextAccessor,
        ReferralService referralService,
        ILogger<SampleDataService> logger)
    {
        _context = context;
        _environment = environment;
        _httpContextAccessor = httpContextAccessor;
        _referralService = referralService;
        _logger = logger;
    }

    /// <summary>
    /// Checks if the application is running in debug mode and on localhost
    /// </summary>
    public bool IsDebugAndLocalhost()
    {
        bool isDebug = false;
#if DEBUG
        isDebug = true;
#endif
        var request = _httpContextAccessor.HttpContext?.Request;
        bool isLocalhost = request != null &&
            (request.Host.Host.Equals("localhost", StringComparison.OrdinalIgnoreCase) ||
             request.Host.Host.Equals("127.0.0.1", StringComparison.OrdinalIgnoreCase));

        return isDebug && isLocalhost && _environment.IsDevelopment();
    }

    /// <summary>
    /// Generates sample data for a newly registered user
    /// </summary>
    public async Task GenerateSampleDataForUserAsync(int userId, bool forceDebug = false)
    {
        if (!forceDebug && !IsDebugAndLocalhost())
        {
            return; // Only generate sample data in debug mode on localhost
        }

        // Get the user
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return;
        }

        // Get all available markets/coins
        var markets = await _context.Markets.Where(m => m.IsActive == 1).ToListAsync();
        if (!markets.Any())
        {
            return;
        }

        // First, make a large initial deposit to the user's account
        await MakeInitialDepositAsync(user);

        // Create wallets for each coin
        await CreateWalletsAsync(userId, markets);

        // Create realistic trading activity
        await CreateRealisticTradesAsync(userId, markets, user);

        // Create withdrawals
        await CreateWithdrawalsAsync(userId, user);

        // Create referred users
        await CreateReferredUsersAsync(userId, user);

        // Create sample RZW savings accounts
        await CreateSampleRzwSavingsAsync(userId, user);
    }

    /// <summary>
    /// Makes a large initial deposit to the user's account
    /// </summary>
    private async Task MakeInitialDepositAsync(User user)
    {
        // Generate a large random amount between 100,000 and 1,000,000 TL
        decimal depositAmount = _random.Next(100000, 1000001);

        // Update user's balance
        user.Balance = depositAmount;
        _context.Users.Update(user);

        // Create a payment record for this deposit
        var now = DateTime.UtcNow;
        var fullName = $"{user.Name} {user.Surname}";

        var initialDeposit = new Deposit
        {
            UserId = user.UserId,
            DepositType = "Bank Transfer",
            Amount = depositAmount,
            FullName = fullName,
            ExtraData = $"Initial deposit for sample data",
            IpAddress = "127.0.0.1",
            CreatedDate = now.AddDays(-_random.Next(30, 60)), // Make it older than other transactions
            ProcessStatus = "Completed",
            Status = DepositStatus.Approved,
            LastOnlineDate = now.AddDays(-_random.Next(30, 60)).AddHours(2) // 2 hours after creation
        };

        _context.Deposits.Add(initialDeposit);
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// Creates wallet entries for each coin for the user
    /// </summary>
    private async Task CreateWalletsAsync(int userId, List<Market> markets)
    {
        var existingWallets = await _context.Wallets.Where(w => w.UserId == userId).ToListAsync();
        var existingCoinIds = existingWallets.Select(w => w.CoinId).ToList();

        var walletsToAdd = new List<Wallet>();

        foreach (var market in markets)
        {
            if (!existingCoinIds.Contains(market.Id))
            {
                // All coin wallets start with zero balance
                // TRY balance is stored in User.Balance, not in Wallet table
                decimal balance = 0m; // Start with zero balance for all coin wallets

                walletsToAdd.Add(new Wallet
                {
                    UserId = userId,
                    CoinId = market.Id,
                    Balance = balance,
                    CreatedDate = DateTime.UtcNow.AddDays(-_random.Next(1, 30)),
                    ModifiedDate = DateTime.UtcNow
                });
            }
        }

        if (walletsToAdd.Any())
        {
            _context.Wallets.AddRange(walletsToAdd);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Creates trade history entries for the user
    /// </summary>
    private async Task CreateTradesAsync(int userId, List<Market> markets)
    {
        var tradesToAdd = new List<Trade>();
        var now = DateTime.UtcNow;

        // Get user's wallets
        var wallets = await _context.Wallets.Where(w => w.UserId == userId).ToListAsync();

        // Get user's TRY balance from User table
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null) return;
        decimal userTryBalance = user.Balance;

        // For each market, create 20 random trades
        foreach (var market in markets)
        {
            var wallet = wallets.FirstOrDefault(w => w.CoinId == market.Id);
            if (wallet == null) continue;

            // Find a trading pair wallet if applicable (e.g., for BTC/USDT, find the USDT wallet)
            Wallet? pairWallet = null;
            Market? pairMarket = null;

            if (!string.IsNullOrEmpty(market.PairCode) && market.Id != 1)
            {
                // Parse the pair code to find the base and quote currencies
                string pairCode = market.PairCode;
                string baseCurrency = string.Empty;
                string quoteCurrency = string.Empty;

                // Common formats: BTCUSDT, BTC/USDT, BTC-USDT
                if (pairCode.Contains("/"))
                {
                    var parts = pairCode.Split('/');
                    if (parts.Length == 2)
                    {
                        baseCurrency = parts[0].Trim();
                        quoteCurrency = parts[1].Trim();
                    }
                }
                else if (pairCode.Contains("-"))
                {
                    var parts = pairCode.Split('-');
                    if (parts.Length == 2)
                    {
                        baseCurrency = parts[0].Trim();
                        quoteCurrency = parts[1].Trim();
                    }
                }
                else
                {
                    // Try to split based on common quote currencies (USDT, BTC, ETH, etc.)
                    var commonQuotes = new[] { "USDT", "BTC", "ETH", "TRY", "USD" };
                    foreach (var quote in commonQuotes)
                    {
                        if (pairCode.EndsWith(quote, StringComparison.OrdinalIgnoreCase))
                        {
                            quoteCurrency = quote;
                            baseCurrency = pairCode.Substring(0, pairCode.Length - quote.Length);
                            break;
                        }
                    }
                }

                // If we successfully parsed the pair code
                if (!string.IsNullOrEmpty(quoteCurrency))
                {
                    // Find the quote currency market
                    pairMarket = markets.FirstOrDefault(m =>
                        m.Coin.Equals(quoteCurrency, StringComparison.OrdinalIgnoreCase) ||
                        m.ShortName.Equals(quoteCurrency, StringComparison.OrdinalIgnoreCase));

                    if (pairMarket != null)
                    {
                        // Find the corresponding wallet
                        pairWallet = wallets.FirstOrDefault(w => w.CoinId == pairMarket.Id);
                    }
                }

                // Fallback to the old method if parsing failed
                if (pairWallet == null)
                {
                    // Try to find a wallet that matches part of the pair code but isn't the current coin or TRY
                    foreach (var w in wallets)
                    {
                        if (w.CoinId != market.Id && w.CoinId != 1 &&
                            w.Coin != null && market.PairCode.Contains(w.Coin.PairCode ?? string.Empty, StringComparison.OrdinalIgnoreCase))
                        {
                            pairWallet = w;
                            pairMarket = w.Coin;
                            break;
                        }
                    }
                }
            }

            for (int i = 0; i < 20; i++)
            {
                // Randomly choose buy or sell
                var tradeType = _random.Next(2) == 0 ? TradeType.Buy : TradeType.Sell;

                // Generate random amounts and rates
                var coinRate = market.Id == 1 ? 1m : market.BuyPrice * (1 + (decimal)(_random.NextDouble() * 0.1 - 0.05));
                var coinAmount = market.Id == 1 ?
                    _random.Next(100, 1000) :
                    (decimal)(_random.NextDouble() * 1);
                var tryAmount = coinAmount * coinRate;

                // Calculate balances
                var previousCoinBalance = wallet.Balance - (tradeType == TradeType.Buy ? coinAmount : 0);
                var newCoinBalance = wallet.Balance + (tradeType == TradeType.Sell ? -coinAmount : 0);
                var previousTryBalance = userTryBalance + (tradeType == TradeType.Buy ? tryAmount : 0);
                var newTryBalance = userTryBalance - (tradeType == TradeType.Sell ? -tryAmount : 0);

                // Calculate wallet balances for the trading pair (if exists)
                decimal previousWalletBalance = 0;
                decimal newWalletBalance = 0;

                if (pairWallet != null && pairMarket != null)
                {
                    previousWalletBalance = pairWallet.Balance;

                    // Calculate a realistic change in the pair wallet based on the trade type and amount
                    if (tradeType == TradeType.Buy)
                    {
                        // When buying the base currency (e.g., BTC in BTC/USDT), we're spending the quote currency (USDT)
                        // So the pair wallet (USDT) balance decreases
                        decimal pairAmount = coinAmount * coinRate; // Convert to quote currency amount
                        newWalletBalance = previousWalletBalance - pairAmount;

                        // Ensure we don't go negative
                        if (newWalletBalance < 0)
                        {
                            // If we would go negative, adjust the previous balance to ensure a positive result
                            previousWalletBalance = pairAmount + (decimal)(_random.NextDouble() * 100);
                            newWalletBalance = previousWalletBalance - pairAmount;
                        }
                    }
                    else // Sell
                    {
                        // When selling the base currency (e.g., BTC in BTC/USDT), we're receiving the quote currency (USDT)
                        // So the pair wallet (USDT) balance increases
                        decimal pairAmount = coinAmount * coinRate; // Convert to quote currency amount
                        newWalletBalance = previousWalletBalance + pairAmount;
                    }

                    // Update the pair wallet balance for subsequent trades
                    pairWallet.Balance = newWalletBalance;
                }

                // Create trade entry
                var tradeDate = now.AddDays(-_random.Next(1, 30)).AddHours(-_random.Next(1, 24));

                tradesToAdd.Add(new Trade
                {
                    UserId = userId,
                    CoinId = market.Id,
                    Type = tradeType,
                    CoinRate = coinRate,
                    CoinAmount = coinAmount,
                    TryAmount = tryAmount,
                    PreviousCoinBalance = previousCoinBalance,
                    NewCoinBalance = newCoinBalance,
                    PreviousBalance = previousTryBalance,
                    NewBalance = newTryBalance,
                    PreviousWalletBalance = previousWalletBalance,
                    NewWalletBalance = newWalletBalance,
                    CreatedDate = tradeDate,
                    IsActive = true
                });

                // Update wallet balances for subsequent trades
                wallet.Balance = newCoinBalance;
                userTryBalance = newTryBalance; // Update user's TRY balance for next iteration
            }
        }

        if (tradesToAdd.Any())
        {
            // Sort by date
            tradesToAdd = tradesToAdd.OrderBy(t => t.CreatedDate).ToList();

            // Update user's TRY balance
            user.Balance = userTryBalance;
            _context.Users.Update(user);

            _context.Trades.AddRange(tradesToAdd);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Creates realistic trading activity for the user
    /// </summary>
    private async Task CreateRealisticTradesAsync(int userId, List<Market> markets, User user)
    {
        var tradesToAdd = new List<Trade>();
        var now = DateTime.UtcNow;

        // Get user's wallets
        var wallets = await _context.Wallets.Where(w => w.UserId == userId).ToListAsync();

        // Get user's TRY balance from User table
        decimal initialTryBalance = user.Balance;
        decimal currentTryBalance = initialTryBalance;
        decimal targetTryBalanceReduction = initialTryBalance * 0.7m; // Use 70% of the balance for trading

        // Create a list of active markets excluding TRY
        var activeMarkets = markets.Where(m => m.Id != 1 && m.IsActive == 1).ToList();
        if (!activeMarkets.Any()) return; // Bu zaten memory'de, async gerekmiyor

        // Track wallet balances for all coins
        var walletBalances = new Dictionary<int, decimal>();
        foreach (var wallet in wallets)
        {
            walletBalances[wallet.CoinId] = wallet.Balance;
        }

        // Create a list to track pair wallets for each market
        var marketPairs = new Dictionary<int, (int PairMarketId, string PairCode)>();
        foreach (var market in activeMarkets)
        {
            if (!string.IsNullOrEmpty(market.PairCode))
            {
                // Parse the pair code
                string pairCode = market.PairCode;
                string quoteCurrency = "";

                // Try to extract the quote currency
                if (pairCode.Contains("/"))
                {
                    quoteCurrency = pairCode.Split('/')[1].Trim();
                }
                else if (pairCode.Contains("-"))
                {
                    quoteCurrency = pairCode.Split('-')[1].Trim();
                }
                else
                {
                    // Try common quote currencies
                    var commonQuotes = new[] { "USDT", "BTC", "ETH", "TRY", "USD" };
                    foreach (var quote in commonQuotes)
                    {
                        if (pairCode.EndsWith(quote, StringComparison.OrdinalIgnoreCase))
                        {
                            quoteCurrency = quote;
                            break;
                        }
                    }
                }

                // Find the market for this quote currency
                if (!string.IsNullOrEmpty(quoteCurrency))
                {
                    var pairMarket = markets.FirstOrDefault(m =>
                        m.Coin.Equals(quoteCurrency, StringComparison.OrdinalIgnoreCase) ||
                        m.ShortName.Equals(quoteCurrency, StringComparison.OrdinalIgnoreCase)); // Bu zaten memory'de, async gerekmiyor

                    if (pairMarket != null)
                    {
                        marketPairs[market.Id] = (pairMarket.Id, pairCode);
                    }
                }
            }
        }

        // Number of trades to generate
        int maxTrades = 200; // Maximum number of trades to generate
        int tradeCount = 0;

        // Generate trades until we've used up the target amount of TRY or reached max trades
        while (initialTryBalance - currentTryBalance < targetTryBalanceReduction && tradeCount < maxTrades)
        {
            // Randomly select a market to trade
            var market = activeMarkets[_random.Next(activeMarkets.Count)];

            // Get the wallet for this coin
            var coinWallet = wallets.FirstOrDefault(w => w.CoinId == market.Id); // Bu zaten memory'de, async gerekmiyor
            if (coinWallet == null) continue;

            // Determine if we should buy or sell based on balances
            bool canBuy = currentTryBalance > 1000; // Ensure we have enough TRY to buy
            bool canSell = walletBalances.ContainsKey(market.Id) && walletBalances[market.Id] > 0; // Ensure we have coins to sell

            TradeType tradeType;
            if (canBuy && canSell)
            {
                // If we can do both, randomly choose but favor buying until we've used up most of our TRY
                double buyProbability = Math.Max(0.1, Math.Min(0.9, (double)(initialTryBalance - currentTryBalance) / (double)targetTryBalanceReduction));
                tradeType = _random.NextDouble() < buyProbability ? TradeType.Sell : TradeType.Buy;
            }
            else if (canBuy)
            {
                tradeType = TradeType.Buy;
            }
            else if (canSell)
            {
                tradeType = TradeType.Sell;
            }
            else
            {
                // Can't trade this coin, try another
                continue;
            }

            // Generate realistic amounts and rates
            decimal coinRate = market.BuyPrice * (1 + (decimal)(_random.NextDouble() * 0.1 - 0.05)); // +/- 5% of market price

            decimal coinAmount;
            decimal tryAmount;

            if (tradeType == TradeType.Buy)
            {
                // When buying, determine how much TRY to spend (between 1% and 10% of current balance)
                tryAmount = currentTryBalance * (decimal)(_random.NextDouble() * 0.09 + 0.01);
                coinAmount = tryAmount / coinRate;
            }
            else // Sell
            {
                // When selling, determine how much of the coin to sell (between 10% and 50% of current balance)
                coinAmount = walletBalances[market.Id] * (decimal)(_random.NextDouble() * 0.4 + 0.1);
                tryAmount = coinAmount * coinRate;
            }

            // Calculate TRY wallet balances
            decimal previousTryBalance = currentTryBalance;
            decimal newTryBalance;

            if (tradeType == TradeType.Buy)
            {
                newTryBalance = currentTryBalance - tryAmount;
            }
            else // Sell
            {
                newTryBalance = currentTryBalance + tryAmount;
            }

            // Calculate coin wallet balances
            decimal previousCoinBalance = walletBalances.ContainsKey(market.Id) ? walletBalances[market.Id] : 0;
            decimal newCoinBalance;

            if (tradeType == TradeType.Buy)
            {
                newCoinBalance = previousCoinBalance + coinAmount;
            }
            else // Sell
            {
                newCoinBalance = previousCoinBalance - coinAmount;
            }

            // Calculate pair wallet balances if applicable
            decimal previousPairBalance = 0;
            decimal newPairBalance = 0;

            if (marketPairs.ContainsKey(market.Id))
            {
                var (pairMarketId, _) = marketPairs[market.Id];

                // Get or initialize the pair wallet balance
                if (walletBalances.ContainsKey(pairMarketId))
                {
                    previousPairBalance = walletBalances[pairMarketId];
                }

                // Calculate the change in the pair wallet
                if (tradeType == TradeType.Buy)
                {
                    // When buying the base currency, we're spending the quote currency
                    newPairBalance = previousPairBalance - tryAmount;

                    // Ensure we don't go negative
                    if (newPairBalance < 0)
                    {
                        // If we would go negative, adjust the previous balance to ensure a positive result
                        previousPairBalance = tryAmount + (decimal)(_random.NextDouble() * 1000);
                        newPairBalance = previousPairBalance - tryAmount;
                    }
                }
                else // Sell
                {
                    // When selling the base currency, we're receiving the quote currency
                    newPairBalance = previousPairBalance + tryAmount;
                }

                // Update the pair wallet balance for subsequent trades
                walletBalances[pairMarketId] = newPairBalance;
            }

            // Create the trade record
            var tradeDate = now.AddDays(-_random.Next(1, 30)).AddHours(-_random.Next(1, 24));

            tradesToAdd.Add(new Trade
            {
                UserId = userId,
                CoinId = market.Id,
                Type = tradeType,
                CoinRate = coinRate,
                CoinAmount = coinAmount,
                TryAmount = tryAmount,
                PreviousCoinBalance = previousCoinBalance,
                NewCoinBalance = newCoinBalance,
                PreviousBalance = previousTryBalance,
                NewBalance = newTryBalance,
                PreviousWalletBalance = previousPairBalance,
                NewWalletBalance = newPairBalance,
                CreatedDate = tradeDate,
                IsActive = true
            });

            // Update balances for next iteration
            currentTryBalance = newTryBalance;
            walletBalances[market.Id] = newCoinBalance;

            tradeCount++;
        }

        if (tradesToAdd.Any())
        {
            // Sort by date
            tradesToAdd = tradesToAdd.OrderBy(t => t.CreatedDate).ToList(); // Bu zaten memory'de, async gerekmiyor

            // Update the user's balance to reflect the final TRY balance
            user.Balance = currentTryBalance;
            _context.Users.Update(user);

            // Update all wallet balances
            foreach (var wallet in wallets)
            {
                if (walletBalances.ContainsKey(wallet.CoinId))
                {
                    wallet.Balance = walletBalances[wallet.CoinId];
                    _context.Wallets.Update(wallet);
                }
            }

            // Save the trades
            _context.Trades.AddRange(tradesToAdd);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Creates payment (deposit) entries for the user
    /// </summary>
    private async Task CreateDepositsAsync(int userId, User user)
    {
        var paymentsToAdd = new List<Deposit>();
        var now = DateTime.UtcNow;
        var fullName = $"{user.Name} {user.Surname}";

        // Get banks for reference
        var banks = await _context.Banks.Where(b => b.IsActive).ToListAsync();
        if (!banks.Any())
        {
            // Add a default bank if none exists
            var bank = new Bank
            {
                BankName = "Sample Bank",
                AccountHolder = "Company Account",
                Iban = "**************************",
                IsActive = true,
                Order = 1,
                CreatedDate = now
            };
            _context.Banks.Add(bank);
            await _context.SaveChangesAsync();
            banks.Add(bank);
        }

        // Create 20 payment entries
        for (int i = 0; i < 20; i++)
        {
            var bank = banks[_random.Next(banks.Count)];
            var amount = _random.Next(100, 10000);
            var status = _random.Next(3); // 0: Pending, 1: Approved, 2: Rejected
            var createdDate = now.AddDays(-_random.Next(1, 30)).AddHours(-_random.Next(1, 24));

            paymentsToAdd.Add(new Deposit
            {
                UserId = userId,
                DepositType = "Bank Transfer",
                Amount = amount,
                FullName = fullName,
                ExtraData = $"Bank: {bank.BankName}, Reference: REF{_random.Next(100000, 999999)}",
                IpAddress = "127.0.0.1",
                CreatedDate = createdDate,
                ProcessStatus = status == 0 ? "Pending" : (status == 1 ? "Completed" : "Rejected"),
                Status = (DepositStatus)status,
                LastOnlineDate = status == 0 ? null : createdDate.AddHours(_random.Next(1, 24))
            });
        }

        if (paymentsToAdd.Any())
        {
            // Sort by date
            paymentsToAdd = paymentsToAdd.OrderBy(p => p.CreatedDate).ToList(); // Bu zaten memory'de, async gerekmiyor
            _context.Deposits.AddRange(paymentsToAdd);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Creates withdrawal entries for the user
    /// </summary>
    private async Task CreateWithdrawalsAsync(int userId, User user)
    {
        var withdrawalsToAdd = new List<Withdrawal>();
        var now = DateTime.UtcNow;
        var fullName = $"{user.Name} {user.Surname}";

        // Get user's TRY balance from User table
        if (user == null) return;
        decimal userBalance = user.Balance;

        // Create 20 withdrawal entries
        for (int i = 0; i < 20; i++)
        {
            var amount = _random.Next(100, 5000);
            var status = _random.Next(3); // 0: Pending, 1: Approved, 2: Rejected
            var createdDate = now.AddDays(-_random.Next(1, 30)).AddHours(-_random.Next(1, 24));

            withdrawalsToAdd.Add(new Withdrawal
            {
                UserId = userId,
                FullName = fullName,
                Email = user.Email,
                Balance = userBalance + amount, // Simulate balance at time of withdrawal
                WithdrawalAmount = amount,
                AccountHolder = fullName,
                Iban = $"TR{_random.Next(********, ********)}{_random.Next(********, ********)}{_random.Next(1000, 9999)}",
                Status = (WithdrawalStatus)status,
                CreatedDate = createdDate
            });
        }

        if (withdrawalsToAdd.Any())
        {
            // Sort by date
            withdrawalsToAdd = withdrawalsToAdd.OrderBy(w => w.CreatedDate).ToList(); // Bu zaten memory'de, async gerekmiyor
            _context.Withdrawals.AddRange(withdrawalsToAdd);
            await _context.SaveChangesAsync();
        }
    }

    /// <summary>
    /// Removes all sample data for a user
    /// </summary>
    public async Task RemoveSampleDataForUserAsync(int userId)
    {
        // Remove trades
        var trades = await _context.Trades.Where(t => t.UserId == userId).ToListAsync();
        if (trades.Any())
        {
            _context.Trades.RemoveRange(trades);
        }

        // Remove payments
        var payments = await _context.Deposits.Where(p => p.UserId == userId).ToListAsync();
        if (payments.Any())
        {
            _context.Deposits.RemoveRange(payments);
        }

        // Remove withdrawals
        var withdrawals = await _context.Withdrawals.Where(w => w.UserId == userId).ToListAsync();
        if (withdrawals.Any())
        {
            _context.Withdrawals.RemoveRange(withdrawals);
        }

        // Remove wallets
        var wallets = await _context.Wallets.Where(w => w.UserId == userId).ToListAsync();
        if (wallets.Any())
        {
            _context.Wallets.RemoveRange(wallets);
        }

        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// Creates sample referred users for the given user
    /// </summary>
    private async Task CreateReferredUsersAsync(int userId, User referrer)
    {
        // Generate between 3 and 8 referred users
        int referredUserCount = _random.Next(3, 9);
        var referredUsers = new List<User>();

        // Make sure the referrer has a referral code
        if (string.IsNullOrEmpty(referrer.ReferralCode))
        {
            referrer.ReferralCode = await _referralService.GenerateUniqueReferralCodeAsync();
            _context.Users.Update(referrer);
            await _context.SaveChangesAsync();
        }

        for (int i = 0; i < referredUserCount; i++)
        {
            // Generate a unique referral code for the new user
            string referralCode = await _referralService.GenerateUniqueReferralCodeAsync();

            // Create a new user with the referrer
            var referredUser = new User
            {
                Email = $"referred{i}_{Guid.NewGuid().ToString().Substring(0, 8)}@example.com",
                Name = $"Referred{i}",
                Surname = $"User{i}",
                PhoneNumber = $"555{_random.Next(1000000, 9999999)}",
                IdentityNumber = $"{_random.Next(********, ********)}",
                BirthDate = DateTime.UtcNow.AddYears(-_random.Next(20, 50)),
                PasswordHash = HashHelper.getHash("123123"),
                IsActive = 1,
                CrDate = DateTime.UtcNow.AddDays(-_random.Next(1, 30)),
                ReferrerId = userId,
                ReferralCode = referralCode,
                Balance = _random.Next(1000, 10000),
                UserRoleRelations = [new UserRoleRelation { RoleId = (int)Roller.User, IsActive = 1 }]
            };

            referredUsers.Add(referredUser);
        }

        if (referredUsers.Any())
        {
            _context.Users.AddRange(referredUsers);
            await _context.SaveChangesAsync();

            // Generate sample data for each referred user
            foreach (var user in referredUsers)
            {
                // Get all available markets/coins
                var markets = await _context.Markets.Where(m => m.IsActive == 1).ToListAsync();
                if (!markets.Any())
                {
                    continue;
                }

                // Create wallets for each coin
                await CreateWalletsAsync(user.UserId, markets);

                // Create some trades for the referred user
                await CreateRealisticTradesAsync(user.UserId, markets, user);
            }
        }
    }

    /// <summary>
    /// Creates sample RZW savings accounts for testing
    /// </summary>
    private async Task CreateSampleRzwSavingsAsync(int userId, User user)
    {
        try
        {
            // Get available RZW savings plans
            var plans = await _context.RzwSavingsPlans.Where(p => p.IsActive).ToListAsync();
            if (!plans.Any())
            {
                return;
            }

            // Get RZW token ID
            var rzwMarket = await _context.Markets.FirstOrDefaultAsync(m => m.Coin == "RZW" && m.IsActive == 1);
            if (rzwMarket == null)
            {
                return;
            }

            // Ensure user has RZW wallet with sufficient balance
            var rzwWallet = await _context.Wallets.FirstOrDefaultAsync(w => w.UserId == userId && w.CoinId == rzwMarket.Id);
            if (rzwWallet == null)
            {
                rzwWallet = new Wallet
                {
                    UserId = userId,
                    CoinId = rzwMarket.Id,
                    Balance = 50000m, // Give user 50,000 RZW for testing
                    LockedBalance = 0m
                };
                _context.Wallets.Add(rzwWallet);
                await _context.SaveChangesAsync();
            }
            else if (rzwWallet.Balance < 10000m)
            {
                rzwWallet.Balance = 50000m; // Ensure sufficient balance
                await _context.SaveChangesAsync();
            }

            // Create sample savings accounts
            var sampleAccounts = new List<RzwSavingsAccount>();
            var now = DateTime.UtcNow;

            // Create 1-2 active accounts
            for (int i = 0; i < Math.Min(2, plans.Count); i++)
            {
                var plan = plans[i];
                var startDate = now.AddDays(-_random.Next(1, plan.TermDuration / 2)); // Started some time ago
                var maturityDate = startDate.AddDays(plan.TermDuration);
                var amount = _random.Next(1000, 5000);

                var account = new RzwSavingsAccount
                {
                    UserId = userId,
                    PlanId = plan.Id,
                    RzwAmount = amount,
                    InterestRate = plan.InterestRate,
                    TermType = plan.TermType,
                    TermDuration = plan.TermDuration,
                    StartDate = startDate,
                    MaturityDate = maturityDate,
                    Status = RzwSavingsStatus.Active,
                    EarlyWithdrawalPenalty = 0.10m,
                    CreatedDate = startDate,
                    TotalEarnedRzw = 0m // Will be calculated by interest service
                };

                sampleAccounts.Add(account);
            }

            // Create 1 completed account if there are enough plans
            if (plans.Count > 2)
            {
                var plan = plans[2];
                var startDate = now.AddDays(-(plan.TermDuration + _random.Next(1, 30))); // Completed some time ago
                var maturityDate = startDate.AddDays(plan.TermDuration);
                var amount = _random.Next(500, 2000);

                var account = new RzwSavingsAccount
                {
                    UserId = userId,
                    PlanId = plan.Id,
                    RzwAmount = amount,
                    InterestRate = plan.InterestRate,
                    TermType = plan.TermType,
                    TermDuration = plan.TermDuration,
                    StartDate = startDate,
                    MaturityDate = maturityDate,
                    Status = RzwSavingsStatus.Matured,
                    EarlyWithdrawalPenalty = 0.10m,
                    CreatedDate = startDate,
                    ModifiedDate = maturityDate,
                    TotalEarnedRzw = amount * plan.InterestRate * plan.TermDuration // Simple interest for completed account
                };

                sampleAccounts.Add(account);
            }

            if (sampleAccounts.Any())
            {
                _context.RzwSavingsAccounts.AddRange(sampleAccounts);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Created {Count} sample RZW savings accounts for user {UserId}",
                    sampleAccounts.Count, userId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating sample RZW savings accounts for user {UserId}", userId);
        }
    }
}
